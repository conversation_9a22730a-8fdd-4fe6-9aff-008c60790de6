import matplotlib.pyplot as plt
import numpy as np
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 根据您的图表数据重建数据点
snr_values = [0, 2.5, 5, 7.5, 10, 12.5, 15, 17.5, 20]

# 从图表中读取的近似数据
urban_alpha = [0.25, 0.38, 0.36, 0.36, 0.33, 0.28, 0.28, 0.27, 0.28]
open_area_alpha = [0.18, 0.35, 0.32, 0.29, 0.27, 0.32, 0.32, 0.29, 0.29]
complex_terrain_alpha = [0.20, 0.30, 0.22, 0.33, 0.29, 0.24, 0.23, 0.24, 0.23]
high_mobility_alpha = [0.24, 0.44, 0.30, 0.42, 0.37, 0.17, 0.35, 0.33, 0.23]

# 创建优化的图表
fig, ax = plt.subplots(figsize=(12, 8))

# 设置专业的颜色方案
colors = {
    'urban': '#2E86AB',           # 深蓝色
    'open_area': '#F24236',       # 橙红色  
    'complex_terrain': '#F6AE2D', # 金黄色
    'high_mobility': '#2F9B69'    # 深绿色
}

# 设置线条样式
line_styles = {
    'urban': '-',
    'open_area': '--', 
    'complex_terrain': '-.',
    'high_mobility': ':'
}

# 设置标记样式
markers = {
    'urban': 'o',
    'open_area': 's',
    'complex_terrain': '^', 
    'high_mobility': 'D'
}

# 绘制线条，增加线宽和标记大小
ax.plot(snr_values, urban_alpha, 
        color=colors['urban'], linestyle=line_styles['urban'], 
        marker=markers['urban'], markersize=8, linewidth=2.5,
        label='城市环境 (Urban)', markerfacecolor='white', 
        markeredgewidth=2, markeredgecolor=colors['urban'])

ax.plot(snr_values, open_area_alpha, 
        color=colors['open_area'], linestyle=line_styles['open_area'], 
        marker=markers['open_area'], markersize=8, linewidth=2.5,
        label='开阔地区 (Open Area)', markerfacecolor='white', 
        markeredgewidth=2, markeredgecolor=colors['open_area'])

ax.plot(snr_values, complex_terrain_alpha, 
        color=colors['complex_terrain'], linestyle=line_styles['complex_terrain'], 
        marker=markers['complex_terrain'], markersize=8, linewidth=2.5,
        label='复杂地形 (Complex Terrain)', markerfacecolor='white', 
        markeredgewidth=2, markeredgecolor=colors['complex_terrain'])

ax.plot(snr_values, high_mobility_alpha, 
        color=colors['high_mobility'], linestyle=line_styles['high_mobility'], 
        marker=markers['high_mobility'], markersize=8, linewidth=2.5,
        label='高速移动 (High Mobility)', markerfacecolor='white', 
        markeredgewidth=2, markeredgecolor=colors['high_mobility'])

# 优化纵坐标范围 - 只显示有数据的部分
y_min = 0.15  # 略低于最小数据点
y_max = 0.47  # 略高于最大数据点
ax.set_ylim(y_min, y_max)

# 设置坐标轴
ax.set_xlabel('信噪比 SNR (dB)', fontsize=14, fontweight='bold')
ax.set_ylabel('BOC功率比 α', fontsize=14, fontweight='bold')
ax.set_title('不同场景下SNR与BOC功率比(α)的关系', fontsize=16, fontweight='bold', pad=20)

# 优化网格
ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
ax.set_axisbelow(True)

# 设置坐标轴刻度
ax.set_xticks(snr_values)
ax.set_xticklabels([f'{int(x)}' for x in snr_values], fontsize=12)

# 优化y轴刻度
y_ticks = np.arange(0.15, 0.48, 0.05)
ax.set_yticks(y_ticks)
ax.set_yticklabels([f'{y:.2f}' for y in y_ticks], fontsize=12)

# 优化图例
legend = ax.legend(loc='upper right', fontsize=12, frameon=True, 
                  fancybox=True, shadow=True, framealpha=0.9,
                  bbox_to_anchor=(0.98, 0.98))
legend.get_frame().set_facecolor('white')
legend.get_frame().set_edgecolor('gray')

# 设置图表边框
for spine in ax.spines.values():
    spine.set_linewidth(1.2)
    spine.set_color('gray')

# 优化布局
plt.tight_layout()

# 保存高质量图片
plt.savefig('中文论文核心/optimized_alpha_snr_relationship.png', 
            dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.savefig('中文论文核心/optimized_alpha_snr_relationship.pdf', 
            bbox_inches='tight', facecolor='white', edgecolor='none')

print("✅ 优化后的图表已保存:")
print("📁 PNG格式: optimized_alpha_snr_relationship.png")
print("📁 PDF格式: optimized_alpha_snr_relationship.pdf")
print("\n🎨 优化内容:")
print("- 调整纵坐标范围至 0.15-0.47，突出数据变化")
print("- 使用专业配色方案和线条样式")
print("- 增强标记和线条视觉效果")
print("- 优化网格和图例显示")
print("- 提升整体图表美观度")

plt.show()
