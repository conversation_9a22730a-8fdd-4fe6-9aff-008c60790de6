#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主仿真控制文件
整合固定功率比基准测试和DQN强化学习优化的完整仿真流程
"""

import os
import sys
import time
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def run_baseline_simulation():
    """运行固定功率比的基准仿真"""
    print("=" * 60)
    print("第一阶段：固定功率比基准测试")
    print("=" * 60)
    print("正在运行固定功率比[(0.1,0.9), (0.3,0.7), (0.5,0.5)]的性能测试...")

    # 导入并运行ceshi.py中的仿真代码
    try:
        # 导入基准测试模块
        from ceshi import run_baseline_simulation as run_baseline

        # 执行基准测试
        baseline_results = run_baseline()

        print("✅ 基准测试完成！")
        print("📊 已生成固定功率比的性能曲线图")
        print("📈 已计算四个性能指标在不同SNR下的表现")
        print("🎯 已建立性能基准线")

        # 生成PPT汇报用的高质量图表
        print("🎨 正在生成PPT汇报用的优化图表...")
        try:
            from enhanced_visualization import create_ppt_ready_charts
            create_ppt_ready_charts()
            print("✅ PPT图表生成完成！")
        except Exception as e:
            print(f"⚠️  PPT图表生成失败: {e}")

        return baseline_results

    except Exception as e:
        print(f"❌ 基准测试出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_dqn_training():
    """运行DQN强化学习训练"""
    print("\n" + "=" * 60)
    print("第二阶段：DQN强化学习训练")
    print("=" * 60)
    print("正在启动DQN智能体训练...")
    
    try:
        # 导入DQN训练模块
        from train_dqn_main import train_dqn_with_translaw, plot_training_results
        
        print("🤖 初始化DQN智能体...")
        print("🧠 开始强化学习训练...")
        
        # 执行DQN训练
        agent, scores, optimal_power_ratios = train_dqn_with_translaw(episodes=150, batch_size=16)
        
        print("✅ DQN训练完成！")
        print("📊 正在生成训练结果图表...")
        
        # 绘制训练结果
        plot_training_results(scores, optimal_power_ratios)
        
        print("🎯 已学习到各场景下的最优功率分配策略")
        
        return agent, scores, optimal_power_ratios
        
    except Exception as e:
        print(f"❌ DQN训练出错: {e}")
        return None, None, None

def run_comparison_analysis(optimal_power_ratios):
    """运行对比分析"""
    print("\n" + "=" * 60)
    print("第三阶段：性能对比分析")
    print("=" * 60)
    print("正在进行固定方法 vs DQN方法的性能对比...")
    
    try:
        # 导入对比分析模块
        from connect_dqn import evaluate_signal_with_dqn_ratios, plot_comparison
        
        print("📊 评估DQN学习的功率比性能...")
        dqn_results, snr_range = evaluate_signal_with_dqn_ratios()
        
        print("📈 生成对比图表...")
        plot_comparison()
        
        print("✅ 对比分析完成！")
        print("🏆 已验证DQN方法相对于固定方法的优势")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比分析出错: {e}")
        return False

def print_summary(optimal_power_ratios):
    """打印仿真总结"""
    print("\n" + "=" * 60)
    print("仿真总结")
    print("=" * 60)
    
    if optimal_power_ratios:
        print("🎯 DQN学习到的最优功率分配策略：")
        scenarios = ["urban", "complex_terrain", "open_area", "high_mobility"]
        scenario_names = {"urban": "城市场景", "complex_terrain": "复杂地形", 
                         "open_area": "开阔地区", "high_mobility": "高速移动"}
        
        for scenario in scenarios:
            if scenario in optimal_power_ratios:
                print(f"\n📍 {scenario_names.get(scenario, scenario)}:")
                snr_data = optimal_power_ratios[scenario]
                for snr in sorted(snr_data.keys()):
                    if snr_data[snr]:
                        alpha, beta = snr_data[snr]
                        print(f"   SNR {snr}dB: α={alpha:.2f}, β={beta:.2f}")
    
    print("\n🎉 完整仿真流程执行完毕！")
    print("📁 所有结果图表和数据已保存到相应文件")

def main():
    """主仿真控制函数"""
    print("🚀 启动BOC内插BPSK复合信号智能优化仿真系统")
    print("📋 仿真流程：基准测试 → DQN训练 → 性能对比")
    
    start_time = time.time()
    
    # 第一阶段：固定功率比基准测试
    baseline_results = run_baseline_simulation()

    if baseline_results is None:
        print("❌ 基准测试失败，终止仿真")
        return

    # 第二阶段：DQN强化学习训练
    agent, scores, optimal_power_ratios = run_dqn_training()

    if optimal_power_ratios is None:
        print("❌ DQN训练失败，跳过对比分析")
        return

    # 第三阶段：性能对比分析
    comparison_success = run_comparison_analysis(optimal_power_ratios)
    
    # 打印总结
    print_summary(optimal_power_ratios)
    
    # 计算总耗时
    total_time = time.time() - start_time
    print(f"\n⏱️  总仿真时间: {total_time:.2f} 秒")

if __name__ == "__main__":
    main()
