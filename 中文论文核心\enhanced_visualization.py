#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版可视化模块
专门用于生成PPT汇报用的高质量图表
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

def create_ppt_ready_charts():
    """生成PPT汇报用的高质量图表"""
    
    # 从ceshi.py导入数据
    from ceshi import (power_ratios, snr_range, ber_results, tracking_error_results,
                      capacity_results, jamming_margin_results, composite_scores,
                      calculate_weights_by_scenario, compute_adaptive_composite_score)
    
    # 1. 创建主要综合性能对比图 - PPT版本
    create_main_performance_chart(power_ratios, snr_range, composite_scores)
    
    # 2. 创建四指标详细分析图 - PPT版本  
    create_detailed_metrics_chart(power_ratios, snr_range, ber_results, 
                                 tracking_error_results, capacity_results, jamming_margin_results)
    
    # 3. 创建场景权重对比图 - 替代雷达图
    create_scenario_weights_comparison()
    
    # 4. 创建场景性能热力图 - 新增
    create_scenario_heatmap(power_ratios, snr_range, composite_scores)

def create_main_performance_chart(power_ratios, snr_range, composite_scores):
    """创建主要综合性能对比图"""
    
    # 重新计算综合评分（使用默认场景）
    scenarios = ["urban", "open_area", "complex_terrain", "high_mobility"]
    from ceshi import compute_adaptive_composite_score, ber_results, tracking_error_results, capacity_results, jamming_margin_results
    
    # 计算默认场景下的综合评分
    default_composite_scores = {}
    for (alpha, beta) in power_ratios:
        default_composite_scores[(alpha, beta)] = compute_adaptive_composite_score(
            ber_results[(alpha, beta)],
            tracking_error_results[(alpha, beta)],
            capacity_results[(alpha, beta)],
            jamming_margin_results[(alpha, beta)],
            snr_range
        )
    
    plt.figure(figsize=(12, 8))
    
    # 定义专业配色方案
    colors = ['#2E86AB', '#A23B72', '#F18F01']  # 蓝、紫、橙
    markers = ['o', 's', '^']
    linestyles = ['-', '--', '-.']
    
    for i, (alpha, beta) in enumerate(power_ratios):
        plt.plot(snr_range, default_composite_scores[(alpha, beta)],
                color=colors[i], marker=markers[i], linestyle=linestyles[i],
                linewidth=4, markersize=10, markeredgewidth=2,
                markeredgecolor='white', 
                label=f'α={alpha}, β={beta}', alpha=0.9)
    
    plt.title('固定功率比基准性能对比', fontsize=20, fontweight='bold', pad=25)
    plt.xlabel('信噪比 SNR (dB)', fontsize=16, fontweight='bold')
    plt.ylabel('综合性能指标', fontsize=16, fontweight='bold')
    
    # 优化图例
    plt.legend(title='功率分配方案', fontsize=14, title_fontsize=15,
              loc='lower right', frameon=True, fancybox=True, shadow=True,
              bbox_to_anchor=(0.98, 0.02))
    
    # 优化网格和坐标轴
    plt.grid(True, alpha=0.3, linestyle='--', linewidth=1)
    plt.xlim(-0.5, 20.5)
    plt.ylim(0, 1.05)
    plt.xticks(range(0, 21, 4), fontsize=14)
    plt.yticks(np.arange(0, 1.1, 0.2), fontsize=14)
    
    # 添加性能区域标注
    plt.axhspan(0.8, 1.0, alpha=0.1, color='green', label='高性能区域')
    plt.axhspan(0.6, 0.8, alpha=0.1, color='yellow', label='中等性能区域')
    plt.axhspan(0, 0.6, alpha=0.1, color='red', label='低性能区域')
    
    plt.tight_layout()
    plt.savefig('figures/ppt_main_performance.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_detailed_metrics_chart(power_ratios, snr_range, ber_results, 
                                 tracking_error_results, capacity_results, jamming_margin_results):
    """创建四指标详细分析图"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('四维性能指标详细分析', fontsize=20, fontweight='bold', y=0.95)
    
    colors = ['#2E86AB', '#A23B72', '#F18F01']
    markers = ['o', 's', '^']
    
    # 指标配置
    metrics = [
        (ber_results, '比特错误率 (BER)', '错误率', axes[0, 0]),
        (tracking_error_results, '码跟踪误差', '跟踪误差', axes[0, 1]),
        (capacity_results, '信道容量', '容量 (bps/Hz)', axes[1, 0]),
        (jamming_margin_results, '抗干扰裕度', '裕度 (dB)', axes[1, 1])
    ]
    
    for metric_data, title, ylabel, ax in metrics:
        for i, (alpha, beta) in enumerate(power_ratios):
            ax.plot(snr_range, metric_data[(alpha, beta)],
                   color=colors[i], marker=markers[i], linewidth=3,
                   markersize=8, markeredgewidth=1.5, markeredgecolor='white',
                   label=f'α={alpha}, β={beta}', alpha=0.9)
        
        ax.set_title(title, fontsize=16, fontweight='bold', pad=15)
        ax.set_xlabel('SNR (dB)', fontsize=14)
        ax.set_ylabel(ylabel, fontsize=14)
        ax.legend(fontsize=12, loc='best')
        ax.grid(True, alpha=0.3, linestyle='--')
        ax.tick_params(labelsize=12)
    
    plt.tight_layout()
    plt.savefig('figures/ppt_detailed_metrics.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_scenario_weights_comparison():
    """创建场景权重对比图 - 替代雷达图"""
    
    from ceshi import calculate_weights_by_scenario
    
    scenarios = ["urban", "open_area", "complex_terrain", "high_mobility"]
    scenario_names = {
        'urban': '城市环境',
        'open_area': '开阔地区',
        'complex_terrain': '复杂地形', 
        'high_mobility': '高速移动'
    }
    
    # 获取各场景的权重
    weights_data = {}
    for scenario in scenarios:
        weights = calculate_weights_by_scenario(scenario, 10)  # 使用中等SNR
        weights_data[scenario] = [
            weights['ber'], weights['tracking'], 
            weights['capacity'], weights['jamming_margin']
        ]
    
    # 创建堆叠柱状图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 左图：堆叠柱状图
    categories = ['BER权重', '跟踪权重', '容量权重', '抗干扰权重']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    x = np.arange(len(scenarios))
    width = 0.6
    
    bottom = np.zeros(len(scenarios))
    for i, category in enumerate(categories):
        values = [weights_data[scenario][i] for scenario in scenarios]
        ax1.bar(x, values, width, bottom=bottom, label=category, 
               color=colors[i], alpha=0.8, edgecolor='white', linewidth=2)
        bottom += values
    
    ax1.set_title('各场景权重分配策略', fontsize=18, fontweight='bold', pad=20)
    ax1.set_xlabel('应用场景', fontsize=14, fontweight='bold')
    ax1.set_ylabel('权重比例', fontsize=14, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels([scenario_names[s] for s in scenarios], fontsize=12)
    ax1.legend(fontsize=12, loc='upper left', bbox_to_anchor=(1.02, 1))
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 右图：热力图
    weights_matrix = np.array([weights_data[scenario] for scenario in scenarios])
    im = ax2.imshow(weights_matrix, cmap='YlOrRd', aspect='auto', alpha=0.8)
    
    ax2.set_title('权重分布热力图', fontsize=18, fontweight='bold', pad=20)
    ax2.set_xticks(range(len(categories)))
    ax2.set_xticklabels(categories, fontsize=12, rotation=45)
    ax2.set_yticks(range(len(scenarios)))
    ax2.set_yticklabels([scenario_names[s] for s in scenarios], fontsize=12)
    
    # 添加数值标注
    for i in range(len(scenarios)):
        for j in range(len(categories)):
            text = ax2.text(j, i, f'{weights_matrix[i, j]:.2f}',
                           ha="center", va="center", color="black", fontweight='bold')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax2, shrink=0.8)
    cbar.set_label('权重值', fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('figures/ppt_scenario_weights.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_scenario_heatmap(power_ratios, snr_range, composite_scores):
    """创建场景性能热力图"""
    
    scenarios = ["urban", "open_area", "complex_terrain", "high_mobility"]
    scenario_names = {
        'urban': '城市环境',
        'open_area': '开阔地区',
        'complex_terrain': '复杂地形',
        'high_mobility': '高速移动'
    }
    
    # 重新计算各场景的综合评分
    from ceshi import (compute_adaptive_composite_score, ber_results, 
                      tracking_error_results, capacity_results, jamming_margin_results)
    
    scenario_composite_scores = {scenario: {} for scenario in scenarios}
    for scenario in scenarios:
        for (alpha, beta) in power_ratios:
            scenario_composite_scores[scenario][(alpha, beta)] = compute_adaptive_composite_score(
                ber_results[(alpha, beta)],
                tracking_error_results[(alpha, beta)],
                capacity_results[(alpha, beta)],
                jamming_margin_results[(alpha, beta)],
                snr_range,
                scenario=scenario
            )
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('各场景下功率比性能热力图', fontsize=20, fontweight='bold', y=0.95)
    
    for i, scenario in enumerate(scenarios):
        ax = axes[i//2, i%2]
        
        # 创建性能矩阵
        performance_matrix = np.zeros((len(power_ratios), len(snr_range)))
        for j, (alpha, beta) in enumerate(power_ratios):
            performance_matrix[j, :] = scenario_composite_scores[scenario][(alpha, beta)]
        
        # 绘制热力图
        im = ax.imshow(performance_matrix, cmap='viridis', aspect='auto', alpha=0.9)
        
        ax.set_title(f'{scenario_names[scenario]}', fontsize=16, fontweight='bold', pad=15)
        ax.set_xlabel('SNR (dB)', fontsize=14)
        ax.set_ylabel('功率比配置', fontsize=14)
        
        # 设置坐标轴
        ax.set_xticks(range(0, len(snr_range), 2))
        ax.set_xticklabels(snr_range[::2], fontsize=12)
        ax.set_yticks(range(len(power_ratios)))
        ax.set_yticklabels([f'α={alpha}, β={beta}' for alpha, beta in power_ratios], fontsize=12)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('性能指标', fontsize=12)
    
    plt.tight_layout()
    plt.savefig('figures/ppt_scenario_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("正在生成PPT汇报用的高质量图表...")
    create_ppt_ready_charts()
    print("✅ 所有PPT图表已生成完成！")
    print("📁 图表保存在 figures/ 目录下")
