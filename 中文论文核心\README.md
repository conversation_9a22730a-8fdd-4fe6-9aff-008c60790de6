# BOC内插BPSK复合信号智能优化仿真系统

## 问题诊断与解决方案

### 问题原因
您的代码被分成了三个独立的文件，但它们之间没有自动连接：

1. **`ceshi.py`** - 固定功率比基准测试（没有主函数调用）
2. **`train_dqn_main.py`** - DQN强化学习训练（独立运行）
3. **`connect_dqn.py`** - 连接和对比分析（假设DQN已训练完成）

### 解决方案
创建了主控制文件 `main_simulation.py` 来整合完整的仿真流程。

## 文件结构

```
中文论文核心/
├── main_simulation.py      # 🚀 主仿真控制文件（新增）
├── ceshi.py               # 📊 固定功率比基准测试
├── train_dqn_main.py      # 🤖 DQN强化学习训练
├── connect_dqn.py         # 📈 性能对比分析
├── signal_env.py          # 🌍 信号环境定义
├── dqn_translaw.py        # 🧠 DQN算法实现
└── README.md              # 📖 使用说明
```

## 运行方式

### 方式1：完整仿真流程（推荐）
```bash
python main_simulation.py
```

这将按顺序执行：
1. **第一阶段**：固定功率比基准测试
2. **第二阶段**：DQN强化学习训练
3. **第三阶段**：性能对比分析

### 方式2：分步骤运行
```bash
# 1. 只运行基准测试
python ceshi.py

# 2. 只运行DQN训练
python train_dqn_main.py

# 3. 只运行对比分析
python connect_dqn.py
```

## 仿真流程说明

### 第一阶段：固定功率比基准测试
- **目的**：建立性能基准线
- **方法**：测试三个固定功率比 [(0.1,0.9), (0.3,0.7), (0.5,0.5)]
- **输出**：
  - 四个性能指标随SNR的变化曲线
  - 不同场景下的综合性能评价
  - 各场景权重分布雷达图

### 第二阶段：DQN强化学习训练
- **目的**：学习最优功率分配策略
- **方法**：基于模糊逻辑三重权重的DQN训练
- **输出**：
  - 训练过程曲线
  - 各场景下SNR与最优功率比的关系
  - 训练好的DQN模型

### 第三阶段：性能对比分析
- **目的**：验证DQN方法的优势
- **方法**：对比固定方法与DQN方法的性能
- **输出**：
  - 性能对比图表
  - 量化的性能提升数据

## 预期输出

运行完整仿真后，您将获得：

1. **图表文件**：
   - 固定功率比性能曲线
   - DQN训练过程图
   - 性能对比图表
   - 3D性能分析图

2. **数据文件**：
   - `dqn_training_results.json` - DQN训练结果
   - `dqn_signal_model.h5` - 训练好的模型

3. **控制台输出**：
   - 各阶段执行状态
   - 最优功率比数据
   - 性能提升统计

## 故障排除

如果遇到问题：

1. **导入错误**：确保安装了所需的Python包
   ```bash
   pip install numpy matplotlib scipy tensorflow
   ```

2. **内存不足**：减少DQN训练的episodes数量
   ```python
   # 在train_dqn_main.py中修改
   episodes=50  # 从150减少到50
   ```

3. **GPU问题**：如果没有GPU，DQN会自动使用CPU训练

## 技术特点

- ✅ **渐进式研究方法**：从简单到复杂
- ✅ **科学对比验证**：量化智能方法优势
- ✅ **完整仿真流程**：一键运行全部测试
- ✅ **模块化设计**：可独立运行各部分
- ✅ **可视化输出**：丰富的图表展示

现在您可以运行 `python main_simulation.py` 来执行完整的仿真流程了！
