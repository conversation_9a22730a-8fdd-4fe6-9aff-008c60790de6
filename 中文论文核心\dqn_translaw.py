import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense, Input
from tensorflow.keras.optimizers import Adam
import matplotlib.pyplot as plt
import random
from collections import deque

# TransLaw 知识迁移框架简化实现
class TransLawFramework:
    def __init__(self, source_domain_knowledge, target_domain_params):
        """
        初始化 TransLaw 框架
        
        参数:
        - source_domain_knowledge: 源域知识，如其他场景的DQN权重或规则
        - target_domain_params: 目标域参数
        """
        self.source_knowledge = source_domain_knowledge
        self.target_params = target_domain_params
        self.transfer_ratio = 0.5  # 知识迁移比例
        
    def adapt_knowledge(self):
        """根据目标域参数调整源域知识"""
        adapted_knowledge = {}
        
        # 根据场景相似度调整迁移比例
        scenario_similarity = self._calculate_scenario_similarity()
        self.transfer_ratio = min(0.8, max(0.2, scenario_similarity))
        
        # 调整网络权重或规则
        for key, value in self.source_knowledge.items():
            if isinstance(value, np.ndarray):
                adapted_knowledge[key] = value * self.transfer_ratio + \
                                         np.random.normal(0, 0.1, value.shape) * (1 - self.transfer_ratio)
            elif isinstance(value, dict):
                adapted_knowledge[key] = {k: v * self.transfer_ratio for k, v in value.items()}
            elif isinstance(value, list) and key == 'network_weights':
                # 处理网络权重列表
                adapted_weights = []
                for weight_array in value:
                    adapted_weight = weight_array * self.transfer_ratio + \
                                    np.random.normal(0, 0.1, weight_array.shape) * (1 - self.transfer_ratio)
                    adapted_weights.append(adapted_weight)
                adapted_knowledge[key] = adapted_weights
            else:
                adapted_knowledge[key] = value
                
        return adapted_knowledge
        
    def _calculate_scenario_similarity(self):
        """计算源域和目标域场景的相似度"""
        # 根据场景参数计算相似度
        source_scenario = self.source_knowledge.get('scenario', 'urban')
        target_scenario = self.target_params.get('scenario', 'urban')
        
        # 场景相似度映射表 (简化版)
        similarity_map = {
            ('urban', 'urban'): 0.9,
            ('urban', 'complex_terrain'): 0.4,
            ('urban', 'open_area'): 0.3,
            ('urban', 'high_mobility'): 0.6,
            ('complex_terrain', 'complex_terrain'): 0.9,
            ('complex_terrain', 'urban'): 0.4,
            ('complex_terrain', 'open_area'): 0.2,
            ('complex_terrain', 'high_mobility'): 0.3,
            ('open_area', 'open_area'): 0.9,
            ('open_area', 'urban'): 0.3,
            ('open_area', 'complex_terrain'): 0.2,
            ('open_area', 'high_mobility'): 0.5,
            ('high_mobility', 'high_mobility'): 0.9,
            ('high_mobility', 'urban'): 0.6,
            ('high_mobility', 'complex_terrain'): 0.3,
            ('high_mobility', 'open_area'): 0.5
        }
        
        return similarity_map.get((source_scenario, target_scenario), 0.5)
    
    def apply_transfer(self, dqn_agent):
        """将调整后的知识应用到目标DQN代理"""
        adapted_knowledge = self.adapt_knowledge()
        
        # 如果有网络权重，应用到代理
        if 'network_weights' in adapted_knowledge:
            # 确保权重维度匹配
            if isinstance(adapted_knowledge['network_weights'], list):
                weights_list = adapted_knowledge['network_weights']
                for i, layer in enumerate(dqn_agent.model.layers):
                    if i < len(weights_list):
                        layer_weights = layer.get_weights()
                        adapted_weights = weights_list[i]
                        
                        # 确保维度匹配
                        if all(w1.shape == w2.shape for w1, w2 in zip(layer_weights, adapted_weights)):
                            layer.set_weights(adapted_weights)
            else:
                # 获取模型当前权重
                model_weights = dqn_agent.model.get_weights()
                # 确保权重形状兼容后应用
                try:
                    dqn_agent.model.set_weights(adapted_knowledge['network_weights'])
                    dqn_agent.target_model.set_weights(adapted_knowledge['network_weights'])
                except Exception as e:
                    print(f"权重迁移失败: {e}")
                    # 恢复原始权重
                    dqn_agent.model.set_weights(model_weights)
        
        # 传递经验回放缓存
        if 'memory_samples' in adapted_knowledge and adapted_knowledge['memory_samples']:
            for sample in adapted_knowledge['memory_samples']:
                dqn_agent.remember(*sample)
                
        return dqn_agent


# DQN强化学习代理
class DQNAgent:
    def __init__(self, state_size, action_size):
        """初始化DQN代理"""
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=2000)  # 增加回放缓冲区大小，提高学习稳定性
        self.gamma = 0.95    # 折扣因子
        self.epsilon = 1.0   # 探索率
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.998  # 减缓探索率衰减，让模型有更多探索机会
        self.learning_rate = 0.001
        self.model = self._build_model()  # Q网络
        self.target_model = self._build_model()  # 目标网络
        self.update_target_model()
        
    def _build_model(self):
        """构建深度Q网络"""
        model = Sequential([
            Input(shape=(self.state_size,)),
            Dense(32, activation='relu'),  # 增加神经元数量
            Dense(32, activation='relu'),  # 增加神经元数量
            Dense(32, activation='relu'),  # 增加一层，提高网络表达能力
            Dense(self.action_size, activation='linear')
        ])
        model.compile(loss='mse', optimizer=Adam(learning_rate=self.learning_rate))
        return model
        
    def update_target_model(self):
        """更新目标网络权重"""
        self.target_model.set_weights(self.model.get_weights())
    
    def remember(self, state, action, reward, next_state, done):
        """将交互经验存储到回放缓存"""
        self.memory.append((state, action, reward, next_state, done))
    
    def act(self, state):
        """选择动作（探索或利用）"""
        if np.random.rand() <= self.epsilon:
            return random.randrange(self.action_size)  # 随机探索
        
        act_values = self.model.predict(state, verbose=0)  # 使用Q网络预测动作价值
        return np.argmax(act_values[0])  # 选择价值最高的动作
    
    def replay(self, batch_size):
        """从经验回放缓存中随机抽样进行训练"""
        if len(self.memory) < batch_size:
            return
        
        # 随机抽样
        minibatch = random.sample(self.memory, batch_size)
        
        states = np.zeros((batch_size, self.state_size))
        targets = np.zeros((batch_size, self.action_size))
        
        for i, (state, action, reward, next_state, done) in enumerate(minibatch):
            # 计算目标Q值
            target = self.model.predict(state, verbose=0)[0]
            
            if done:
                target[action] = reward  # 终止状态
            else:
                # 使用目标网络计算下一状态的最大Q值
                t = self.target_model.predict(next_state, verbose=0)[0]
                target[action] = reward + self.gamma * np.amax(t)
            
            states[i] = state
            targets[i] = target
        
        # 批量训练Q网络，提高效率
        self.model.fit(states, targets, epochs=1, batch_size=batch_size, verbose=0)
        
        # 衰减探索率
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
            
    def load(self, name):
        """加载模型权重"""
        self.model.load_weights(name)
        self.target_model.load_weights(name)
        
    def save(self, name):
        """保存模型权重"""
        self.model.save_weights(name)