import numpy as np
import sys
import os

# 添加父目录到系统路径以导入ceshi.py中的函数
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ceshi import (generate_composite_signal, awgn_noise, rayleigh_fading_channel,
                  sync_boc_signal_kalman, calculate_code_tracking_error, 
                  calculate_channel_capacity, calculate_jamming_margin,
                  bpsk_demodulation, calculate_ber, adaptive_importance,
                  calculate_weights_by_scenario)

# 定义信号环境
class SignalEnvironment:
    def __init__(self, max_snr=20, scenarios=["urban", "open_area", "complex_terrain", "high_mobility"]):
        # 环境参数
        self.snr_range = np.arange(0, max_snr+1, 2)  
        self.current_snr_index = 0
        self.current_scenario_index = 0
        self.scenarios = scenarios
        self.current_scenario = scenarios[0]
        
        # 信号参数
        self.fs = 5e6
        self.duration = 1e-3
        self.sampling_rate = 10e5  # 降低采样率从100MHz到10MHz
        self.t = np.arange(0, self.duration, 1 / self.sampling_rate)
        
        # 生成PN序列和BPSK数据
        self.pn_sequence_boc = np.random.choice([-1, 1], size=len(self.t))
        self.bpsk_data = np.random.choice([-1, 1], size=len(self.t))
        
        # 动作空间: 不同的功率比例 (alpha, beta)
        self.power_ratios = [(0.1, 0.9), (0.2, 0.8), (0.3, 0.7), (0.4, 0.6), (0.5, 0.5), 
                            (0.6, 0.4), (0.7, 0.3), (0.8, 0.2), (0.9, 0.1)]
        
        # 状态空间: [SNR, 场景指标, 当前BER, 当前码跟踪误差, 当前信道容量, 当前抗多径能力]
        self.state_size = 6
        self.action_size = len(self.power_ratios)
        
        # 初始状态
        self.state = self._get_initial_state()
        
    def _get_initial_state(self):
        """获取初始状态"""
        # 初始SNR
        snr = self.snr_range[self.current_snr_index]
        
        # 场景索引 (数字编码)
        scenario_idx = self.current_scenario_index
        
        # 初始性能指标 (设置为较差值)
        initial_ber = 0.5
        initial_tracking_error = 20.0
        initial_channel_capacity = 1.0  # 较低的信道容量(差)
        initial_jamming_margin = 1.0  # 较低的抗干扰裕度(差)
        
        return np.array([snr, scenario_idx, initial_ber, initial_tracking_error, 
                        initial_channel_capacity, initial_jamming_margin])
    
    def reset(self):
        """重置环境"""
        self.current_snr_index = 0
        self.current_scenario_index = np.random.randint(len(self.scenarios))
        self.current_scenario = self.scenarios[self.current_scenario_index]
        self.state = self._get_initial_state()
        return self.state.reshape(1, -1)
    
    def step(self, action):
        """执行动作并返回下一个状态、奖励和是否结束"""
        # 获取当前动作对应的功率比
        alpha, beta = self.power_ratios[action]
        
        # 获取当前SNR和场景
        snr = self.snr_range[self.current_snr_index]
        scenario = self.current_scenario
        
        # 生成信号并添加噪声
        composite_signal, boc_signal, bpsk_signal = generate_composite_signal(
            alpha, beta, self.pn_sequence_boc, self.bpsk_data, self.fs, self.t)
        noisy_signal = awgn_noise(composite_signal, snr)
        faded_signal = rayleigh_fading_channel(noisy_signal)
        
        # 计算性能指标
        true_offset = 0
        code_offset, estimated_code_offset = sync_boc_signal_kalman(
            faded_signal, self.pn_sequence_boc, self.fs, self.sampling_rate)
        tracking_error = calculate_code_tracking_error(estimated_code_offset, true_offset)
        channel_capacity = calculate_channel_capacity(faded_signal, snr, alpha)
        jamming_margin = calculate_jamming_margin(faded_signal, snr, alpha)
        bpsk_received = bpsk_demodulation(faded_signal)
        bpsk_received = np.clip(bpsk_received, -1, 1)
        ber = calculate_ber(self.bpsk_data, bpsk_received)
        
        # 计算奖励 (使用自适应模糊函数)
        # 对于奖励函数，我们直接使用已有的自适应评价指标
        ber_importance = adaptive_importance(ber, 'ber', snr)
        tracking_importance = adaptive_importance(tracking_error, 'tracking', snr)
        capacity_importance = adaptive_importance(channel_capacity, 'capacity', snr)
        jamming_importance = adaptive_importance(jamming_margin, 'jamming_margin', snr)
        
        # 获取权重
        weights = calculate_weights_by_scenario(scenario, snr)
        
        # 归一化性能指标 (注意BER、tracking_error值越小越好，channel_capacity和jamming_margin值越大越好)
        norm_ber = 1 - min(ber, 0.5) / 0.5  # BER范围[0, 0.5]
        norm_tracking = 1 - min(tracking_error, 30) / 30  # 跟踪误差范围[0, 30]
        norm_capacity = (channel_capacity - 1) / 9  # 信道容量范围[1, 10]，已是正向指标，值越大越好
        norm_jamming = (jamming_margin - 1) / 9  # 抗干扰裕度范围[1, 10]，已是正向指标，值越大越好
        
        # 计算奖励
        reward = (weights['ber'] * norm_ber * (1 + 0.5 * ber_importance) + 
                 weights['tracking'] * norm_tracking * (1 + 0.5 * tracking_importance) + 
                 weights['capacity'] * norm_capacity * (1 + 0.5 * capacity_importance) + 
                 weights['jamming_margin'] * norm_jamming * (1 + 0.5 * jamming_importance))
        
        # 更新状态
        self.state = np.array([snr, self.current_scenario_index, ber, 
                              tracking_error, channel_capacity, jamming_margin])
        
        # 检查是否需要切换到下一个SNR
        done = False
        self.current_snr_index += 1
        if self.current_snr_index >= len(self.snr_range):
            self.current_snr_index = 0
            done = True
        
        return self.state.reshape(1, -1), reward, done, {'ber': ber, 'tracking': tracking_error, 
                                                       'capacity': channel_capacity, 
                                                       'jamming_margin': jamming_margin} 