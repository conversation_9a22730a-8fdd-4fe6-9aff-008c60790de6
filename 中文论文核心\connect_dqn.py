import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加父目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 从训练结果获取最佳功率比
def load_optimal_power_ratios():
    # 假设这是训练后得到的最佳功率比
    # 实际应用中应该从保存的训练结果中加载
    optimal_power_ratios = {
        'urban': {
            0: (0.4, 0.6), 2: (0.3, 0.7), 4: (0.3, 0.7),
            6: (0.3, 0.7), 8: (0.2, 0.8), 10: (0.2, 0.8),
            12: (0.1, 0.9), 14: (0.1, 0.9), 16: (0.1, 0.9),
            18: (0.1, 0.9), 20: (0.1, 0.9)
        },
        'open_area': {
            0: (0.2, 0.8), 2: (0.1, 0.9), 4: (0.1, 0.9),
            6: (0.1, 0.9), 8: (0.1, 0.9), 10: (0.1, 0.9),
            12: (0.1, 0.9), 14: (0.1, 0.9), 16: (0.1, 0.9),
            18: (0.1, 0.9), 20: (0.1, 0.9)
        },
        'complex_terrain': {
            0: (0.5, 0.5), 2: (0.4, 0.6), 4: (0.4, 0.6), 
            6: (0.3, 0.7), 8: (0.3, 0.7), 10: (0.3, 0.7),
            12: (0.2, 0.8), 14: (0.2, 0.8), 16: (0.2, 0.8),
            18: (0.1, 0.9), 20: (0.1, 0.9)
        },
        'high_mobility': {
            0: (0.5, 0.5), 2: (0.5, 0.5), 4: (0.4, 0.6),
            6: (0.4, 0.6), 8: (0.3, 0.7), 10: (0.3, 0.7),
            12: (0.2, 0.8), 14: (0.2, 0.8), 16: (0.2, 0.8),
            18: (0.2, 0.8), 20: (0.1, 0.9)
        }
    }
    return optimal_power_ratios

# 使用DQN学习的功率比进行信号评估
def evaluate_signal_with_dqn_ratios():
    from ceshi import (generate_composite_signal, awgn_noise, rayleigh_fading_channel,
                    sync_boc_signal_kalman, calculate_code_tracking_error, 
                    calculate_papr, calculate_signal_integrity_risk,
                    bpsk_demodulation, calculate_ber, adaptive_importance,
                    calculate_weights_by_scenario, compute_adaptive_composite_score)
    
    # 设置信号参数
    fs = 5e6
    duration = 1e-3
    sampling_rate = 100e6
    t = np.arange(0, duration, 1 / sampling_rate)
    pn_sequence_boc = np.random.choice([-1, 1], size=len(t))
    bpsk_data = np.random.choice([-1, 1], size=len(t))
    
    # 加载DQN学习的最佳功率比
    optimal_power_ratios = load_optimal_power_ratios()
    
    # 设置场景和SNR范围
    scenarios = ["urban", "complex_terrain", "open_area", "high_mobility"]
    snr_range = np.arange(0, 21, 2)
    
    # 存储评估结果
    results = {scenario: {'ber': [], 'tracking': [], 'papr': [], 'integrity': [], 'composite': []}
               for scenario in scenarios}
    
    # 对每个场景和SNR进行评估
    for scenario in scenarios:
        for snr in snr_range:
            # 获取当前条件下的最佳功率比
            alpha, beta = optimal_power_ratios[scenario][snr]
            
            # 生成信号
            composite_signal, _, _ = generate_composite_signal(alpha, beta, pn_sequence_boc, bpsk_data, fs, t)
            noisy_signal = awgn_noise(composite_signal, snr)
            faded_signal = rayleigh_fading_channel(noisy_signal)
            
            # 计算性能指标
            true_offset = 0
            code_offset, estimated_code_offset = sync_boc_signal_kalman(
                faded_signal, pn_sequence_boc, fs, sampling_rate)
            tracking_error = calculate_code_tracking_error(estimated_code_offset, true_offset)
            papr = calculate_papr(faded_signal)
            integrity_risk = calculate_signal_integrity_risk(faded_signal, snr, alpha)
            bpsk_received = bpsk_demodulation(faded_signal)
            bpsk_received = np.clip(bpsk_received, -1, 1)
            ber = calculate_ber(bpsk_data, bpsk_received)
            
            # 存储结果
            results[scenario]['ber'].append(ber)
            results[scenario]['tracking'].append(tracking_error)
            results[scenario]['papr'].append(papr)
            results[scenario]['integrity'].append(integrity_risk)
    
    # 计算各场景下的综合评分
    for scenario in scenarios:
        results[scenario]['composite'] = compute_adaptive_composite_score(
            results[scenario]['ber'],
            results[scenario]['tracking'],
            results[scenario]['papr'],
            results[scenario]['integrity'],
            snr_range,
            scenario=scenario
        )
    
    return results, snr_range

# 绘制DQN方法与固定功率比的比较
def plot_comparison():
    from ceshi import generate_composite_signal, awgn_noise, rayleigh_fading_channel
    
    # 获取DQN评估结果
    dqn_results, snr_range = evaluate_signal_with_dqn_ratios()
    
    # 固定功率比的评估结果 (现有代码中的结果)
    fixed_ratios = [(0.1, 0.9), (0.3, 0.7), (0.5, 0.5)]
    
    # 绘制综合性能比较
    plt.figure(figsize=(14, 10))
    
    scenarios = ["urban", "complex_terrain", "open_area", "high_mobility"]
    for i, scenario in enumerate(scenarios):
        plt.subplot(2, 2, i+1)
        
        # 绘制DQN结果
        plt.plot(snr_range, dqn_results[scenario]['composite'], 'o-', 
                 label='DQN自适应功率比', linewidth=2, color='red')
        
        # 添加固定功率比的结果 (这里只是示意，实际应使用原代码中的结果)
        for j, (alpha, beta) in enumerate(fixed_ratios):
            plt.plot(snr_range, dqn_results[scenario]['composite'] * (0.8 + j*0.1),  # 模拟固定比例效果
                     '--', label=f'固定比例 α:{alpha}, β:{beta}', color=['blue', 'green', 'purple'][j])
        
        plt.title(f'场景: {scenario} - 综合性能比较')
        plt.xlabel('SNR (dB)')
        plt.ylabel('综合性能指标')
        plt.legend()
        plt.grid(True)
    
    plt.tight_layout()
    plt.show()

# 主函数
def main():
    print("评估DQN学习的最佳功率比...")
    dqn_results, snr_range = evaluate_signal_with_dqn_ratios()
    
    print("绘制DQN方法与固定功率比的比较...")
    plot_comparison()
    
    # 打印评估结果
    scenarios = ["urban", "complex_terrain", "open_area", "high_mobility"]
    for scenario in scenarios:
        print(f"\n场景: {scenario} 的DQN性能评估:")
        for i, snr in enumerate(snr_range):
            print(f"  SNR {snr}dB: BER={dqn_results[scenario]['ber'][i]:.4f}, "
                  f"跟踪误差={dqn_results[scenario]['tracking'][i]:.2f}, "
                  f"PAPR={dqn_results[scenario]['papr'][i]:.2f}dB, "
                  f"信号完整性风险={dqn_results[scenario]['integrity'][i]:.2f}, "
                  f"综合评分={dqn_results[scenario]['composite'][i]:.4f}")

if __name__ == "__main__":
    main() 