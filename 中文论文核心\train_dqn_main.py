import numpy as np
import matplotlib.pyplot as plt
from dqn_translaw import DQNAgent, TransLawFramework
from signal_env import SignalEnvironment
import tensorflow as tf
import json
import os
import matplotlib as mpl

# 设置中文字体支持
mpl.rcParams['font.sans-serif'] = ['SimHei']
mpl.rcParams['axes.unicode_minus'] = False

def train_dqn_with_translaw(episodes=200, batch_size=16, transfer_learning=True):
    """训练DQN代理并使用TransLaw进行知识迁移"""
    # 创建环境
    env = SignalEnvironment()
    
    # 初始化代理
    agent = DQNAgent(env.state_size, env.action_size)
    
    # 如果使用知识迁移，加载源域知识
    if transfer_learning:
        # 假设我们有来自城市场景的源域知识
        source_knowledge = {
            'scenario': 'urban',
            'network_weights': agent.model.get_weights(),  # 简化，实际应从预训练模型加载
            'memory_samples': []  # 简化，实际应包含关键经验样本
        }
        
        # 对于不同目标场景，应用知识迁移
        for scenario in env.scenarios:
            if scenario != 'urban':  # 跳过源域场景
                target_params = {'scenario': scenario}
                translaw = TransLawFramework(source_knowledge, target_params)
                agent = translaw.apply_transfer(agent)
    
    # 存储训练结果
    scores = []
    best_power_ratios = {scenario: {snr: [] for snr in env.snr_range} for scenario in env.scenarios}
    
    for e in range(episodes):
        state = env.reset()
        total_reward = 0
        
        # 记录当前场景
        current_scenario = env.scenarios[env.current_scenario_index]
        
        # 为每个SNR点记录最佳奖励和动作
        best_rewards = {snr: -float('inf') for snr in env.snr_range}
        best_actions = {snr: 0 for snr in env.snr_range}
        
        done = False
        while not done:
            # 选择动作
            action = agent.act(state)
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            
            # 存储经验
            agent.remember(state, action, reward, next_state, done)
            
            # 更新状态和总奖励
            state = next_state
            total_reward += reward
            
            # 获取当前SNR
            current_snr = env.snr_range[env.current_snr_index - 1]
            
            # 如果当前SNR获得的奖励更高，更新最佳动作
            if reward > best_rewards[current_snr]:
                best_rewards[current_snr] = reward
                best_actions[current_snr] = action
            
            # 经验回放训练
            agent.replay(batch_size)
            
            # 如果回合结束
            if done:
                # 更新目标网络
                agent.update_target_model()
                
                # 打印当前回合的进度信息
                print(f"回合: {e}/{episodes}, 奖励: {total_reward:.2f}, ε: {agent.epsilon:.4f}")
                
                # 记录所有SNR点的最佳功率比
                for snr, best_action in best_actions.items():
                    if best_rewards[snr] > -float('inf'):  # 确保这个SNR点有有效动作
                        best_power_ratios[current_scenario][snr].append(
                            env.power_ratios[best_action])
        
        # 记录回合总奖励
        scores.append(total_reward)
        
        # 每10回合清理一次GPU内存
        if e % 10 == 0:
            # 清理GPU内存
            tf.keras.backend.clear_session()
    
    # 保存模型
    agent.save("dqn_signal_model.h5")
    
    # 保存训练过程的奖励值
    with open("dqn_training_rewards.json", 'w') as f:
        json.dump({"rewards": scores}, f, indent=4)
    
    # 计算各场景和SNR下的最佳功率比
    optimal_power_ratios = {}
    for scenario in best_power_ratios:
        optimal_power_ratios[scenario] = {}
        for snr in best_power_ratios[scenario]:
            if best_power_ratios[scenario][snr]:
                # 取平均值作为最佳功率比
                power_ratios = np.array(best_power_ratios[scenario][snr])
                optimal_power_ratios[scenario][snr] = np.mean(power_ratios, axis=0).tolist()
    
    # 保存结果到JSON文件
    save_results_to_json(optimal_power_ratios, scores, "dqn_training_results.json")
    
    return agent, scores, optimal_power_ratios


def save_results_to_json(optimal_power_ratios, scores, filename):
    """将训练结果保存为JSON文件"""
    # 将numpy数组转换为列表以便JSON序列化
    serializable_results = {
        "optimal_power_ratios": {},
        "scores": scores
    }
    
    for scenario in optimal_power_ratios:
        serializable_results["optimal_power_ratios"][scenario] = {}
        for snr in optimal_power_ratios[scenario]:
            # 确保键是字符串（JSON要求）
            snr_key = str(int(snr))
            if isinstance(optimal_power_ratios[scenario][snr], list):
                serializable_results["optimal_power_ratios"][scenario][snr_key] = optimal_power_ratios[scenario][snr]
            elif isinstance(optimal_power_ratios[scenario][snr], np.ndarray):
                serializable_results["optimal_power_ratios"][scenario][snr_key] = optimal_power_ratios[scenario][snr].tolist()
    
    # 保存为JSON文件
    with open(filename, 'w') as f:
        json.dump(serializable_results, f, indent=4)
    
    print(f"训练结果已保存到 {filename}")


def load_results_from_json(filename):
    """从JSON文件加载训练结果"""
    with open(filename, 'r') as f:
        results = json.load(f)
    
    return results["optimal_power_ratios"], results["scores"]


# 绘制训练过程
def plot_training_results(scores, optimal_power_ratios):
    # 绘制训练奖励曲线
    plt.figure(figsize=(10, 6))
    plt.plot(scores)
    plt.title('DQN 强化学习训练过程', fontsize=14)
    plt.xlabel('回合', fontsize=12)
    plt.ylabel('总奖励', fontsize=12)
    plt.grid(True)
    plt.tight_layout()
    plt.savefig('dqn_training_curve.png', dpi=300)
    plt.show()
    
    # 绘制各场景下SNR与功率比的关系
    plt.figure(figsize=(16, 12))
    scenarios = list(optimal_power_ratios.keys())
    
    for i, scenario in enumerate(scenarios):
        plt.subplot(2, 2, i+1)
        
        # 收集所有SNR点的数据
        snr_values = sorted([float(snr) for snr in optimal_power_ratios[scenario].keys() 
                            if optimal_power_ratios[scenario][snr]])
        
        alpha_values = [optimal_power_ratios[scenario][snr][0] for snr in snr_values]
        beta_values = [optimal_power_ratios[scenario][snr][1] for snr in snr_values]
        
        plt.plot(snr_values, alpha_values, 'o-', label='α (BOC功率比)', linewidth=2, markersize=8)
        plt.plot(snr_values, beta_values, 's-', label='β (BPSK功率比)', linewidth=2, markersize=8)
        
        plt.title(f'场景: {scenario} - 不同SNR下的最佳功率比', fontsize=14)
        plt.xlabel('SNR (dB)', fontsize=12)
        plt.ylabel('功率比例', fontsize=12)
        plt.legend(fontsize=12)
        plt.grid(True)
        plt.ylim(0, 1.0)
    
    plt.tight_layout()
    plt.savefig('dqn_power_ratios_by_scenario.png', dpi=300)
    plt.show()
    
    # 绘制单独的α值对比图
    plt.figure(figsize=(12, 8))
    markers = ['o', 's', '^', 'd']
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    
    for i, scenario in enumerate(scenarios):
        snr_values = sorted([float(snr) for snr in optimal_power_ratios[scenario].keys() 
                            if optimal_power_ratios[scenario][snr]])
        alpha_values = [optimal_power_ratios[scenario][snr][0] for snr in snr_values]
        
        plt.plot(snr_values, alpha_values, marker=markers[i], linestyle='-', 
                 label=f'{scenario}', color=colors[i], linewidth=2, markersize=8)
    
    plt.title('不同场景下SNR与BOC功率比(α)的关系', fontsize=16)
    plt.xlabel('SNR (dB)', fontsize=14)
    plt.ylabel('BOC功率比(α)', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True)
    plt.ylim(0, 1.0)
    plt.tight_layout()
    plt.savefig('dqn_alpha_comparison.png', dpi=300)
    plt.show()


# 与原始方法进行比较
def compare_with_original_method(dqn_power_ratios=None):
    # 从原始方法中获取的最佳功率比
    original_power_ratios = {
        'urban': {0: (0.3, 0.7), 8: (0.3, 0.7), 16: (0.1, 0.9)},
        'complex_terrain': {0: (0.5, 0.5), 8: (0.3, 0.7), 16: (0.3, 0.7)},
        'open_area': {0: (0.1, 0.9), 8: (0.1, 0.9), 16: (0.1, 0.9)},
        'high_mobility': {0: (0.5, 0.5), 8: (0.3, 0.7), 16: (0.3, 0.7)}
    }
    
    # 获取DQN学习的最佳功率比
    if dqn_power_ratios is None:
        agent, scores, dqn_power_ratios = train_dqn_with_translaw(episodes=200, batch_size=16)
    
    # 绘制比较结果
    scenarios = ['urban', 'complex_terrain', 'open_area', 'high_mobility']
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    for i, scenario in enumerate(scenarios):
        row, col = i // 2, i % 2
        ax = axes[row, col]
        
        # 获取这个场景下的原始功率比和DQN功率比
        orig_snr = sorted([float(snr) for snr in original_power_ratios[scenario].keys()])
        orig_alpha = [original_power_ratios[scenario][snr][0] for snr in orig_snr]
        
        dqn_snr = sorted([float(snr) for snr in dqn_power_ratios[scenario].keys() 
                          if dqn_power_ratios[scenario][snr]])
        dqn_alpha = [dqn_power_ratios[scenario][snr][0] for snr in dqn_snr]
        
        # 绘制对比图
        ax.plot(orig_snr, orig_alpha, 'o-', label='原始方法', color='blue', linewidth=2, markersize=8)
        ax.plot(dqn_snr, dqn_alpha, 's--', label='DQN方法', color='red', linewidth=2, markersize=8)
        
        ax.set_title(f'场景: {scenario} - BOC功率比(α)对比', fontsize=14)
        ax.set_xlabel('SNR (dB)', fontsize=12)
        ax.set_ylabel('BOC功率比(α)', fontsize=12)
        ax.legend(fontsize=12)
        ax.grid(True)
        ax.set_ylim(0, 0.6)
    
    plt.tight_layout()
    plt.savefig('dqn_vs_original_comparison.png', dpi=300)
    plt.show()
    
    # 绘制性能对比热图
    create_performance_heatmap(dqn_power_ratios, original_power_ratios)


def create_performance_heatmap(dqn_power_ratios, original_power_ratios):
    """创建性能对比热图"""
    # 计算DQN方法与原始方法的功率比差异
    scenarios = ['urban', 'complex_terrain', 'open_area', 'high_mobility']
    snr_points = [0, 8, 16]  # 使用与原始方法相同的SNR点进行对比
    
    # 准备热图数据
    diff_matrix = np.zeros((len(scenarios), len(snr_points)))
    
    for i, scenario in enumerate(scenarios):
        for j, snr in enumerate(snr_points):
            # 获取原始方法的α值
            orig_alpha = original_power_ratios[scenario][snr][0]
            
            # 获取DQN方法的α值（找最接近的SNR点）
            closest_snr = min(dqn_power_ratios[scenario].keys(), 
                              key=lambda x: abs(float(x) - snr))
            dqn_alpha = dqn_power_ratios[scenario][closest_snr][0]
            
            # 计算相对差异（正值表示DQN值更高）
            diff_matrix[i, j] = (dqn_alpha - orig_alpha) / max(orig_alpha, 0.1)
    
    # 绘制热图
    plt.figure(figsize=(10, 8))
    plt.imshow(diff_matrix, cmap='RdBu_r', aspect='auto', vmin=-1, vmax=1)
    
    # 添加标签
    plt.xticks(np.arange(len(snr_points)), [f'{snr} dB' for snr in snr_points])
    plt.yticks(np.arange(len(scenarios)), scenarios)
    plt.xlabel('SNR', fontsize=14)
    plt.ylabel('场景', fontsize=14)
    plt.title('DQN vs 原始方法：BOC功率比(α)相对差异', fontsize=16)
    
    # 添加颜色条
    cbar = plt.colorbar()
    cbar.set_label('相对差异 (DQN - 原始)/原始', fontsize=12)
    
    # 在热图单元格中显示具体数值
    for i in range(len(scenarios)):
        for j in range(len(snr_points)):
            plt.text(j, i, f'{diff_matrix[i, j]:.2f}', 
                     ha='center', va='center', 
                     color='white' if abs(diff_matrix[i, j]) > 0.5 else 'black')
    
    plt.tight_layout()
    plt.savefig('power_ratio_difference_heatmap.png', dpi=300)
    plt.show()


# 创建3D图表展示
def create_3d_performance_plot(optimal_power_ratios):
    """创建3D图表展示不同场景和SNR下的性能"""
    from mpl_toolkits.mplot3d import Axes3D
    
    # 准备数据
    scenarios = list(optimal_power_ratios.keys())
    scenario_indices = {scenario: i for i, scenario in enumerate(scenarios)}
    
    all_data = []
    for scenario in scenarios:
        for snr in optimal_power_ratios[scenario]:
            if optimal_power_ratios[scenario][snr]:
                alpha, beta = optimal_power_ratios[scenario][snr]
                all_data.append((scenario_indices[scenario], float(snr), alpha))
    
    x = [d[0] for d in all_data]  # 场景索引
    y = [d[1] for d in all_data]  # SNR值
    z = [d[2] for d in all_data]  # α值
    
    # 创建3D图表
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 为不同场景使用不同颜色
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    for i, scenario in enumerate(scenarios):
        scenario_data = [(d[0], d[1], d[2]) for d in all_data if d[0] == i]
        if scenario_data:
            s_x = [d[0] for d in scenario_data]
            s_y = [d[1] for d in scenario_data]
            s_z = [d[2] for d in scenario_data]
            ax.scatter(s_x, s_y, s_z, c=colors[i], s=100, label=scenario)
            
            # 添加拟合曲面
            if len(s_y) > 3:  # 至少需要4个点才能拟合
                from scipy.interpolate import griddata
                x_range = np.linspace(i-0.2, i+0.2, 10)
                y_range = np.linspace(min(s_y), max(s_y), 10)
                X, Y = np.meshgrid(x_range, y_range)
                points = np.array([[x, y] for x, y in zip(s_x, s_y)])
                Z = griddata(points, s_z, (X, Y), method='cubic')
                ax.plot_surface(X, Y, Z, color=colors[i], alpha=0.3)
    
    # 设置图表标签
    ax.set_xlabel('场景', fontsize=12)
    ax.set_ylabel('SNR (dB)', fontsize=12)
    ax.set_zlabel('BOC功率比(α)', fontsize=12)
    ax.set_title('不同场景和SNR下的BOC功率比(α)', fontsize=16)
    
    # 设置刻度标签
    ax.set_xticks(range(len(scenarios)))
    ax.set_xticklabels(scenarios)
    
    plt.legend(fontsize=12)
    plt.tight_layout()
    plt.savefig('3d_performance_plot.png', dpi=300)
    plt.show()


# 主函数
def main():
    print("开始训练DQN代理...")
    agent, scores, optimal_power_ratios = train_dqn_with_translaw(episodes=150, batch_size=16)
    
    # 清理内存
    tf.keras.backend.clear_session()
    import gc
    gc.collect()
    
    print("绘制训练结果...")
    plot_training_results(scores, optimal_power_ratios)
    
    # 清理内存
    tf.keras.backend.clear_session()
    gc.collect()
    
    print("比较与原始方法的差异...")
    compare_with_original_method(optimal_power_ratios)
    
    # 创建3D性能图表
    print("创建3D性能图表...")
    create_3d_performance_plot(optimal_power_ratios)
    
    # 打印各场景下的最佳功率比
    for scenario, snr_data in optimal_power_ratios.items():
        print(f"\n场景: {scenario}")
        for snr, ratio in sorted(snr_data.items()):
            if ratio:  # 确保有有效数据
                print(f"  SNR {snr}dB: α={ratio[0]:.2f}, β={ratio[1]:.2f}")


if __name__ == "__main__":
    main() 